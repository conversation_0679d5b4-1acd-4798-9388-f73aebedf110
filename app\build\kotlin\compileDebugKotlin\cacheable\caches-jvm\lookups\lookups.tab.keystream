  Activity android.app  MyApplicationTheme android.app.Activity  TodoApp android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  MyApplicationTheme android.content.Context  TodoApp android.content.Context  
setContent android.content.Context  MyApplicationTheme android.content.ContextWrapper  TodoApp android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  MyApplicationTheme  android.view.ContextThemeWrapper  TodoApp  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  MyApplicationTheme #androidx.activity.ComponentActivity  TodoApp #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MyApplicationTheme "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  TodoApp "androidx.compose.foundation.layout  TodoItem "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Checkbox .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TodoItem .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  filter .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  map .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  TodoItem .androidx.compose.foundation.lazy.LazyItemScope  filter .androidx.compose.foundation.lazy.LazyItemScope  map .androidx.compose.foundation.lazy.LazyItemScope  TodoItem .androidx.compose.foundation.lazy.LazyListScope  filter .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  map .androidx.compose.foundation.lazy.LazyListScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Bundle androidx.compose.material3  Card androidx.compose.material3  Checkbox androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  
LazyColumn androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MyApplicationTheme androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  Text androidx.compose.material3  TodoApp androidx.compose.material3  TodoItem androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  filter androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  map androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  spacedBy androidx.compose.material3  weight androidx.compose.material3  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Bundle androidx.compose.runtime  Card androidx.compose.runtime  Checkbox androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  
LazyColumn androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  MyApplicationTheme androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Spacer androidx.compose.runtime  Text androidx.compose.runtime  TodoApp androidx.compose.runtime  TodoItem androidx.compose.runtime  	TopAppBar androidx.compose.runtime  Unit androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  filter androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  listOf androidx.compose.runtime  map androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  weight androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  MyApplicationTheme #androidx.core.app.ComponentActivity  TodoApp #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	Alignment com.example.myapplication  Arrangement com.example.myapplication  Boolean com.example.myapplication  Bundle com.example.myapplication  Card com.example.myapplication  Checkbox com.example.myapplication  Column com.example.myapplication  ComponentActivity com.example.myapplication  
Composable com.example.myapplication  ExperimentalMaterial3Api com.example.myapplication  FloatingActionButton com.example.myapplication  Icon com.example.myapplication  
IconButton com.example.myapplication  Icons com.example.myapplication  Int com.example.myapplication  
LazyColumn com.example.myapplication  MainActivity com.example.myapplication  
MaterialTheme com.example.myapplication  Modifier com.example.myapplication  MyApplicationTheme com.example.myapplication  OptIn com.example.myapplication  OutlinedTextField com.example.myapplication  Row com.example.myapplication  Scaffold com.example.myapplication  Spacer com.example.myapplication  String com.example.myapplication  Text com.example.myapplication  TodoApp com.example.myapplication  TodoItem com.example.myapplication  	TopAppBar com.example.myapplication  Unit com.example.myapplication  fillMaxSize com.example.myapplication  fillMaxWidth com.example.myapplication  filter com.example.myapplication  getValue com.example.myapplication  height com.example.myapplication  
isNotBlank com.example.myapplication  listOf com.example.myapplication  map com.example.myapplication  mutableStateOf com.example.myapplication  padding com.example.myapplication  plus com.example.myapplication  provideDelegate com.example.myapplication  remember com.example.myapplication  setValue com.example.myapplication  spacedBy com.example.myapplication  weight com.example.myapplication  MyApplicationTheme &com.example.myapplication.MainActivity  TodoApp &com.example.myapplication.MainActivity  
setContent &com.example.myapplication.MainActivity  copy "com.example.myapplication.TodoItem  id "com.example.myapplication.TodoItem  isCompleted "com.example.myapplication.TodoItem  title "com.example.myapplication.TodoItem  Boolean "com.example.myapplication.ui.theme  Build "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  
FontFamily "com.example.myapplication.ui.theme  
FontWeight "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  
BigDecimal 	java.math  
BigInteger 	java.math  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  OptIn kotlin  Result kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  map kotlin  plus kotlin  not kotlin.Boolean  sp 
kotlin.Double  invoke kotlin.Function0  	compareTo 
kotlin.Int  plus 
kotlin.Int  
isNotBlank 
kotlin.String  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  filter kotlin.collections  listOf kotlin.collections  map kotlin.collections  plus kotlin.collections  filter kotlin.collections.List  map kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  KMutableProperty0 kotlin.reflect  Sequence kotlin.sequences  filter kotlin.sequences  map kotlin.sequences  plus kotlin.sequences  filter kotlin.text  
isNotBlank kotlin.text  map kotlin.text  plus kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        