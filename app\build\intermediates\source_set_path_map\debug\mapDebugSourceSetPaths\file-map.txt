com.example.myapplication.app-runtime-saveable-release-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05ede6508d5fa2e4c31381d82f0f5c82\transformed\runtime-saveable-release\res
com.example.myapplication.app-lifecycle-livedata-core-2.6.1-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08259b85abeef817d4628eda540817e9\transformed\lifecycle-livedata-core-2.6.1\res
com.example.myapplication.app-lifecycle-viewmodel-savedstate-2.6.1-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a65f17f32ffd0e385644fc684d5b718\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.example.myapplication.app-ui-util-release-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0b270d593ae2c6fd929e4f14fadb16de\transformed\ui-util-release\res
com.example.myapplication.app-annotation-experimental-1.4.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1025fdec53f42a3b1baec296861e444c\transformed\annotation-experimental-1.4.0\res
com.example.myapplication.app-savedstate-ktx-1.2.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e4361f59b5abc9109bcf0935baef32\transformed\savedstate-ktx-1.2.1\res
com.example.myapplication.app-material-icons-core-release-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16c1161af96986b63ea5353ea7133d37\transformed\material-icons-core-release\res
com.example.myapplication.app-animation-release-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fa0e2b3020cb5ff555ca1cfb7c91698\transformed\animation-release\res
com.example.myapplication.app-ui-geometry-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29b5a86133d992459d197d64e83d0fba\transformed\ui-geometry-release\res
com.example.myapplication.app-lifecycle-process-2.6.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3612b0ad2d757f69bfa24dc2c3088e9f\transformed\lifecycle-process-2.6.1\res
com.example.myapplication.app-material3-release-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bdae484d8629b91b71d2a8e711d605e\transformed\material3-release\res
com.example.myapplication.app-core-runtime-2.2.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ae577d56c7a6b6cbbd333be755128cb\transformed\core-runtime-2.2.0\res
com.example.myapplication.app-customview-poolingcontainer-1.0.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b5ad589dfae2725b355be2db74e092c\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplication.app-core-ktx-1.12.0-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\574fb3b22c97666acee013c7ead937e9\transformed\core-ktx-1.12.0\res
com.example.myapplication.app-startup-runtime-1.1.1-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f74880b7e4dc2ea23396745126b8a52\transformed\startup-runtime-1.1.1\res
com.example.myapplication.app-lifecycle-viewmodel-ktx-2.6.1-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d3dc86770bfd6124bbe787fb7025666\transformed\lifecycle-viewmodel-ktx-2.6.1\res
com.example.myapplication.app-ui-test-manifest-1.6.6-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7122f04723363524ebfb93d2197207ef\transformed\ui-test-manifest-1.6.6\res
com.example.myapplication.app-savedstate-1.2.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7560741b5a7322061b41ba4c34c2636c\transformed\savedstate-1.2.1\res
com.example.myapplication.app-ui-text-release-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bc43ef36ef2e03e91db7842da6753a2\transformed\ui-text-release\res
com.example.myapplication.app-profileinstaller-1.3.0-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c839e768df8976229a61f1c35850af9\transformed\profileinstaller-1.3.0\res
com.example.myapplication.app-emoji2-1.3.0-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c99f34d6b8482bfcbb773832aa0cf7d\transformed\emoji2-1.3.0\res
com.example.myapplication.app-core-1.12.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f76b6ee10c363ab237345ec85acc133\transformed\core-1.12.0\res
com.example.myapplication.app-ui-graphics-release-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8ac3969aad945845796070ea0de60daa\transformed\ui-graphics-release\res
com.example.myapplication.app-ui-tooling-preview-release-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97941244d0e9699216d8c0f799984a00\transformed\ui-tooling-preview-release\res
com.example.myapplication.app-foundation-layout-release-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9b11460080e1d7f0e385f7bcc26bd9b3\transformed\foundation-layout-release\res
com.example.myapplication.app-activity-1.8.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ece416787e8579b8ec47dfcdc6a9cbd\transformed\activity-1.8.0\res
com.example.myapplication.app-foundation-release-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a575f2fdfb28ee7d8146806f00191903\transformed\foundation-release\res
com.example.myapplication.app-ui-unit-release-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac4c511404cbb5b7e9e3b5cb70751194\transformed\ui-unit-release\res
com.example.myapplication.app-lifecycle-runtime-2.6.1-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ac5f65555a225025a8a702b14d6d9f6a\transformed\lifecycle-runtime-2.6.1\res
com.example.myapplication.app-animation-core-release-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf424f4bd841f98dbb7ceca5d9c6cfa1\transformed\animation-core-release\res
com.example.myapplication.app-activity-ktx-1.8.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c617eb06af8b9901026f792d3adc3524\transformed\activity-ktx-1.8.0\res
com.example.myapplication.app-lifecycle-viewmodel-2.6.1-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc75303be7f17c62fea7bf0391524192\transformed\lifecycle-viewmodel-2.6.1\res
com.example.myapplication.app-ui-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d223efed7c9b71e5170a4e85d2ac6918\transformed\ui-release\res
com.example.myapplication.app-ui-tooling-release-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cf0741a1dd4001a29bb4db0de5ca7\transformed\ui-tooling-release\res
com.example.myapplication.app-lifecycle-runtime-ktx-2.6.1-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d423cc2d37e105c77094b7c9bbbf743f\transformed\lifecycle-runtime-ktx-2.6.1\res
com.example.myapplication.app-material-release-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d753ba01cbcf41a62ffa3740d4e0e54f\transformed\material-release\res
com.example.myapplication.app-runtime-release-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4b01574edba0836818a1a9d62b1b1ef\transformed\runtime-release\res
com.example.myapplication.app-activity-compose-1.8.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed265b39fbceefd941249b541928e20c\transformed\activity-compose-1.8.0\res
com.example.myapplication.app-material-ripple-release-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f07c7f5fd6d886868408f5a3c2274fff\transformed\material-ripple-release\res
com.example.myapplication.app-ui-tooling-data-release-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6fb8bd32822d6bb35cac9f5a3827fbe\transformed\ui-tooling-data-release\res
com.example.myapplication.app-pngs-40 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\pngs\debug
com.example.myapplication.app-resValues-41 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\generated\res\resValues\debug
com.example.myapplication.app-packageDebugResources-42 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplication.app-packageDebugResources-43 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplication.app-debug-44 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplication.app-debug-45 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\debug\res
com.example.myapplication.app-main-46 C:\Users\<USER>\AndroidStudioProjects\MyApplication\app\src\main\res
