{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-42:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a575f2fdfb28ee7d8146806f00191903\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8510,8600", "endColumns": "89,88", "endOffsets": "8595,8684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f76b6ee10c363ab237345ec85acc133\\transformed\\core-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,400,503,609,714,8139", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "195,297,395,498,604,709,829,8235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3bdae484d8629b91b71d2a8e711d605e\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4633,4718,4824,4904,4990,5091,5195,5289,5393,5480,5589,5690,5797,5914,5994,6098", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4628,4713,4819,4899,4985,5086,5190,5284,5388,5475,5584,5685,5792,5909,5989,6093,6192"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1449,1569,1691,1805,1924,2023,2124,2242,2375,2495,2643,2730,2831,2925,3024,3140,3267,3373,3508,3641,3772,3947,4073,4192,4313,4435,4530,4627,4747,4881,4986,5089,5194,5325,5460,5568,5671,5748,5844,5940,6027,6112,6218,6298,6384,6485,6589,6683,6787,6874,6983,7084,7191,7308,7388,7492", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "1564,1686,1800,1919,2018,2119,2237,2370,2490,2638,2725,2826,2920,3019,3135,3262,3368,3503,3636,3767,3942,4068,4187,4308,4430,4525,4622,4742,4876,4981,5084,5189,5320,5455,5563,5666,5743,5839,5935,6022,6107,6213,6293,6379,6480,6584,6678,6782,6869,6978,7079,7186,7303,7383,7487,7586"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d223efed7c9b71e5170a4e85d2ac6918\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,963,1029,1111,1196,1268,1345,1416", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,958,1024,1106,1191,1263,1340,1411,1533"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,927,1007,1103,1198,1280,1358,7591,7682,7766,7834,7900,7982,8067,8240,8317,8388", "endColumns": "92,79,95,94,81,77,90,90,83,67,65,81,84,71,76,70,121", "endOffsets": "922,1002,1098,1193,1275,1353,1444,7677,7761,7829,7895,7977,8062,8134,8312,8383,8505"}}]}]}